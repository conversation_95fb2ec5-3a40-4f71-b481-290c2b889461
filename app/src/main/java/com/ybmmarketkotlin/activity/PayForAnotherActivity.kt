package com.ybmmarketkotlin.activity

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.text.TextUtils
import android.util.Log
import android.view.View
import androidx.activity.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import butterknife.OnClick
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.github.mzule.activityrouter.annotation.Router
import com.ybm.app.bean.NetError
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybm.app.common.SmartExecutorManager
import com.ybmmarket20.R
import com.ybmmarket20.adapter.FindSameGoodsAdapter
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.PayForAnotherBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.Abase
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.*
import com.ybmmarket20.utils.AdapterUtils.addLocalTimeForRows
import com.ybmmarket20.utils.AdapterUtils.changeOrderFreeShippingTags
import com.ybmmarket20.utils.AdapterUtils.getAfterDiscountPrice
import com.ybmmarketkotlin.viewmodel.PayResultViewModel
import com.ybmmarketkotlin.views.PayForAnotherSharePopWindow
import com.ybmmarketkotlin.views.SHARED_TYPE_AP
import com.ybmmarketkotlin.views.SHARED_TYPE_COPY
import com.ybmmarketkotlin.views.SHARED_TYPE_WX
import kotlinx.android.synthetic.main.activity_pay_for_another.crv_pfa_recommend
import kotlinx.android.synthetic.main.activity_pay_for_another.smartrefresh


@Router("payforanother", "payforanother/:orderNo")
class PayForAnotherActivity : BaseActivity() {
    var orderNo: String = ""

    private var pageNum = 1
    private var pageSize = 20
    private val mViewModel: PayResultViewModel by viewModels()
    private var recommendList: MutableList<RowsBean> = ArrayList()
    private var recommendAdapter: FindSameGoodsAdapter? = null
    private var scmid: String = "";


    override fun getContentViewId(): Int {
        return R.layout.activity_pay_for_another
    }

    override fun initData() {
        setTitle("提交结果")

        recommendAdapter = FindSameGoodsAdapter(recommendList, this)

        //初始化推荐列表
        recommendListLoad()

        //初始化观察者
        initObserver()

        //获取推荐商品流
        getRecommendGoods()

        orderNo = intent.getStringExtra("orderNo") ?: ""

        smartrefresh.setOnRefreshListener {
            pageNum = 1;
            getRecommendGoods()
        }

        // 设置SmartRefreshLayout的加载更多功能
        smartrefresh.setOnLoadMoreListener {
            pageNum++;
            getRecommendGoods()
        }
    }
//    private fun getShareCount(v: View) {
//        val merchantId = SpUtil.getMerchantid()
//        val params = RequestParams().apply {
//            put("merchantId", merchantId)
//            put("orderNo", orderNo)
//        }
//        HttpManager.getInstance().post(AppNetConfig.PAY_FOR_ANOTHER_GET_SHARE_URL, params, object : BaseResponse<PayForAnotherBean>() {
//            override fun onSuccess(content: String?, baseBean: BaseBean<PayForAnotherBean>?, data: PayForAnotherBean?) {
//                super.onSuccess(content, baseBean, data)
//                if (baseBean != null && baseBean.isSuccess && data != null) {
//                    PayForAnotherSharePopWindow(data.wechatShowState==1,data.aliPayShowState==1, object : PayForAnotherSharePopWindow.DialogItemClick {
//                        override fun click(content: String) {
//                            when(content){
//                                "linkurl"->{//复制链接
//                                    YbmCommand.setClipboardMsg(data.shareUrl)
//                                    ToastUtils.showShortSafe(getString(R.string.str_pay_for_another_copy_tips))
//                                }
//                                "wx"->{//分享到微信好友
//                                    SmartExecutorManager.getInstance().execute {
//                                        try {
//                                            val bitmap: Bitmap = ImageHelper.with(this@PayForAnotherActivity).load(data.imageUrl).asBitmap().placeholder(R.color.white).dontAnimate().dontTransform().diskCacheStrategy(DiskCacheStrategy.SOURCE).into(100, 100).get()
//                                            ShareUtil.shareWXPage(data.title, data.shareUrl, data.desc, bitmap)
//                                        }catch (e:Exception){
//                                            val bmp = BitmapFactory.decodeResource(Abase.getResources(), R.drawable.logo)
//                                            ShareUtil.shareWXPage(data.title, data.shareUrl, data.desc, bmp)
//                                        }
//
//                                    }
//
//                                }
//                            }
//                        }
//                    }).show(v)
//                }
//            }
//
//            override fun onFailure(error: NetError) {
//                ToastUtils.showShortSafe("获取订单支付链接失败，请重试")
//            }
//        })
//    }

    private fun getShareCount2(v: View) {
        val merchantId = SpUtil.getMerchantid()
        val params = RequestParams().apply {
            put("merchantId", merchantId)
            put("orderNo", orderNo)
        }
        HttpManager.getInstance().post(
            AppNetConfig.PAY_FOR_ANOTHER_GET_SHARE_URL_LIST,
            params,
            object : BaseResponse<List<PayForAnotherBean>>() {
                override fun onSuccess(
                    content: String?,
                    baseBean: BaseBean<List<PayForAnotherBean>>?,
                    data: List<PayForAnotherBean>?
                ) {
                    super.onSuccess(content, baseBean, data)
                    Log.d("share_tag", data.toString());
                    if (baseBean != null && baseBean.isSuccess && data != null) {
                        PayForAnotherSharePopWindow(
                            data,
                            object : PayForAnotherSharePopWindow.DialogItemClick {
                                override fun click(bean: PayForAnotherBean) {
                                    val payAnotherShareData = ShareBitmapUtils.PayAnotherShareData()
                                    payAnotherShareData.apply {
                                        if (bean != null) {
                                            shopName = bean.shopName
                                            payAmountDesc = bean.payAmountDesc
                                            createTimeDesc = bean.createTimeDesc
                                        }
                                    }
                                    when (bean.channelType) {
                                        SHARED_TYPE_WX -> {
                                            SmartExecutorManager.getInstance().execute {
                                                val bitmap = try {
                                                    val bit: Bitmap =
                                                        ShareBitmapUtils.getShareBitmap(
                                                            <EMAIL>,
                                                            payAnotherShareData
                                                        )
                                                    if (bit == null) {
                                                        ImageHelper.with(this@PayForAnotherActivity)
                                                            .load(bean.imageUrl).asBitmap()
                                                            .placeholder(R.color.white)
                                                            .dontAnimate().dontTransform()
                                                            .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                                                            .into(100, 100).get()
                                                    }
                                                    bit
                                                } catch (e: Exception) {
                                                    BitmapFactory.decodeResource(
                                                        Abase.getResources(),
                                                        R.drawable.logo
                                                    )
                                                }
                                                if (bean.isMiniProg == 1) {
                                                    //微信小程序
                                                    ShareUtil.shareWXMiniProg(
                                                        bean.title,
                                                        bean.shareUrl,
                                                        bean.path,
                                                        bean.appId,
                                                        bean.desc,
                                                        bitmap,
                                                        false
                                                    )
                                                } else {
                                                    ShareUtil.shareWXPage(
                                                        bean.title,
                                                        bean.shareUrl,
                                                        bean.desc,
                                                        bitmap
                                                    )
                                                }
                                            }
                                        }

                                        SHARED_TYPE_AP -> {
                                            SmartExecutorManager.getInstance().execute {
                                                try {
                                                    ShareUtil.shareAliPayPage(
                                                        <EMAIL>,
                                                        bean.title,
                                                        bean.shareUrl,
                                                        bean.desc,
                                                        bean.imageUrl
                                                    )
                                                } catch (e: Exception) {
                                                    e.printStackTrace()
                                                }
                                            }
                                        }

                                        SHARED_TYPE_COPY -> {
                                            YbmCommand.setClipboardMsg(bean.shareUrl)
                                            ToastUtils.showShort(getString(R.string.str_pay_for_another_copy_tips))
                                        }
                                    }
                                }
                            }).show(v)
                    }
                }

                override fun onFailure(error: NetError) {
                    ToastUtils.showShort("获取订单支付链接失败，请重试")
                }
            })
    }

    /**
     * 添加观察者
     */
    private fun initObserver() {
        //推荐商品流
        mViewModel.payListRecommendViewModel.observe(this, Observer { data ->
            smartrefresh.setEnableLoadMore(true);
            if (smartrefresh?.isRefreshing == true) {
                scmid = "";
                recommendList.clear();
                smartrefresh.finishRefresh()
            }

            // 完成SmartRefreshLayout的加载更多
            if (smartrefresh?.isLoading == true) {
                smartrefresh.finishLoadMore()
            }

            //如果返回的列表是空则返回并且加载更多功能
            if (data?.rows == null) {
                smartrefresh.finishLoadMoreWithNoMoreData()
                // 兜底，防止崩溃
                return@Observer
            }

            //如果返回的终止字段为true则去掉加载更多功能
            if (data.isEnd) {
                smartrefresh.finishLoadMoreWithNoMoreData()
            }

            val rows = data.getDataList();
            if (rows!!.isNotEmpty()) {
                addLocalTimeForRows(rows)
                changeOrderFreeShippingTags(rows)
                scmid = data.scmId;
                recommendList.addAll(rows)
                // 请求并更新折后价
                getAfterDiscountPrice(
                    recommendList,
                    recommendAdapter!!,
                    true
                )
            } else {
                // 没有更多数据，禁用加载更多
                smartrefresh.finishLoadMoreWithNoMoreData()
            }
            recommendAdapter?.notifyDataSetChanged()
        })
    }

    /**
     * 初始化推荐列表
     */
    private fun recommendListLoad() {
        // 使用 WrapGridLayoutManager 以便在 ScrollView 中正确测量高度
        crv_pfa_recommend.layoutManager =
            StaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL)
        crv_pfa_recommend.adapter = recommendAdapter
        recommendAdapter?.setEnableLoadMore(false)
    }

    /**
     * 获取推荐商品流
     */
    private fun getRecommendGoods() {
        val mParamsMap = hashMapOf(
            "recommendScene" to "7",
            "pageNum" to pageNum.toString(),
            "pageSize" to pageSize.toString(),
        )
        if(pageNum > 1){
            mParamsMap["scmId"] = scmid
        }
        smartrefresh.setEnableLoadMore(false);
        mViewModel.getPayListRecommendGoodlist(mParamsMap)
    }

    @OnClick(R.id.rtv_pay_for_another, R.id.rtv_order)
    fun onClick(v: View?) {
        when (v?.id) {
            //他人代付
            R.id.rtv_pay_for_another -> {
                getShareCount2(v)
            }
            //去订单列表
            R.id.rtv_order -> {
                RoutersUtils.open("ybmpage://myorderlist/0")
            }
        }
    }
}