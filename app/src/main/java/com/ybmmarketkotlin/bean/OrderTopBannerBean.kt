package com.ybmmarketkotlin.bean

import com.ybmmarket20.bean.CouponsTipsResponse
import com.ybmmarket20.bean.RebateVoucherResult
import com.ybmmarket20.bean.ShoppingGoldRechargeBean

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2025/8/20 17:32
 *    desc   :
 */

enum class BannerType {
    // 智慧脸广告
    IMAGE,
    // 优惠券
    CUSTOM_VIEW_COUPON,
    // 消费返
    CUSTOM_VIEW_REBATE,
    // 资质
    CUSTOM_VIEW_APTITUDE,
    // 购物金
    CUSTOM_VIEW_RED_PACK
}

data class OrderTopBannerBean(
    val type: BannerType,
    //消费返横条
    val consumeRebateDetail: RebateVoucherResultWithLevel? = null,
    //购物金充值
    val rechargeDiscount: ShoppingGoldRechargeBeanWithLevel? = null,
    //优惠券横条
    val sceneResponse: CouponsTipsResponseWithLevel? = null,
    //资质
    val licenseRemind: OrderTopBannerLicenseRemindBeanWithLevel? = null,
    //saas广告
    val saasAd: OrderTopBannerAppSaasAdVoWithLevel? = null,
)

data class OrderTopBannerAppSaasAdVoWithLevel(
    val data: OrderTopBannerAppSaasAdVo? = null,
    val level: Int? = 0
)

data class RebateVoucherResultWithLevel(
    val data: RebateVoucherResult? = null,
    val level: Int? = 0
)

data class ShoppingGoldRechargeBeanWithLevel(
    val data: ShoppingGoldRechargeBean? = null,
    val level: Int? = 0
)

data class CouponsTipsResponseWithLevel(
    val data: CouponsTipsResponse? = null,
    val level: Int? = 0
)

data class OrderTopBannerLicenseRemindBeanWithLevel(
    val data: OrderTopBannerLicenseRemindBean? = null,
    val level: Int? = 0
)

data class OrderTopBannerAppSaasAdVo(
    //saas图片地址（可能是多张）
    val saasImageUrl: String? = "",
    //saas跳转地址
    val saasUrl: String? = ""
)

data class OrderTopBannerLicenseRemindBean(
    val title: String? = "",
    val msg: String? = "",
    val type: String? = "",
    val status: String? = ""
)