package com.ybmmarket20.adapter

import android.content.Context
import android.graphics.Bitmap
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.viewpager.widget.PagerAdapter
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.animation.GlideAnimation
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.target.Target
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybmmarket20.R
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.view.ZoomImageView
import java.lang.Exception

class AptitudeXyyImageAdapter(val mContext: Context, val list: MutableList<String>): PagerAdapter() {
    var data = mutableListOf<ImageView>()
    init {
        data = list.map {
            ZoomImageView(mContext)
       }.toMutableList()
    }

    override fun getCount(): Int = list.size

    override fun isViewFromObject(view: View, `object`: Any): Boolean = view == `object`

    override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
        container.removeView(data[position])
    }

    override fun instantiateItem(container: ViewGroup, position: Int): Any {
        val iv = data[position]
//        ImageUtil.load(mContext, list[position], iv)
        ImageHelper.with(mContext).load(list[position]).asBitmap().placeholder(R.drawable.jiazaitu_min).error(R.drawable.jiazaitu_min)
            .diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().dontTransform()
            .into(object : SimpleTarget<Bitmap>(500, 500) {
                override fun onResourceReady(
                    resource: Bitmap?,
                    glideAnimation: GlideAnimation<in Bitmap>?
                ) {
                    iv.setImageBitmap(resource)
                }

            })
        container.addView(iv)
        return iv
    }
}